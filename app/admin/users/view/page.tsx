"use client"
import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { useSupabase } from '@/hooks/useSupabase'; // ✅ Use singleton client
import { useAuth } from '@/contexts/AuthContext'; // ✅ Use auth context
import { PlusCircle, Search, ArrowUpDown, PencilIcon, Eye, X, Mail } from 'lucide-react';
import EmailComposer from '@/components/EmailComposer';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import Link from 'next/link';
import { toast } from 'react-hot-toast';
import TagManager from '@/components/tags/TagManager';
import TagFilter from '@/components/tags/TagFilter';
import { PelatesEdit } from '@/components/users/PelatesEdit';
import type { Database } from '@/types/supabase';

type Pelates = Database['public']['Tables']['pelates']['Row']
type Tag = Database['public']['Tables']['tags']['Row']

interface UserRole {
  auth_user_id: string;
  roles: {
    name: string;
  }
}

interface PelatesWithTags extends Pelates {
  pelates_tags?: {
    tags: Tag
  }[]
  active_subscriptions?: {
    subscription_status: string
  }[]
  user_roles?: UserRole[]
}

// Create a type for the form data that includes subscription_status
interface PelatesFormData extends Partial<Pelates> {
  subscription_status?: string
}

type SortKey = keyof PelatesWithTags | 'subscription_status';

const PelatesPage: React.FC = () => {
  // ✅ Use auth context and singleton client
  const { loading: authLoading, user, userRole } = useAuth();
  const { supabaseClient: supabase } = useSupabase();

  // State management
  const [pelates, setPelates] = useState<PelatesWithTags[]>([]);
  const [selectedPelatis, setSelectedPelatis] = useState<Pelates | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [formData, setFormData] = useState<PelatesFormData>({});
  const [searchTerm, setSearchTerm] = useState('');
  const [sortKey, setSortKey] = useState<SortKey>('client_name');
  const [sortAsc, setSortAsc] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [sidebarRef, setSidebarRef] = useState<HTMLDivElement | null>(null);
  const [selectedFilterTags, setSelectedFilterTags] = useState<Tag[]>([]);
  const [editingPelatis, setEditingPelatis] = useState<PelatesWithTags | null>(null);
  const [selectedClients, setSelectedClients] = useState<PelatesWithTags[]>([]);
  const [showEmailComposer, setShowEmailComposer] = useState(false);
  const [dataLoading, setDataLoading] = useState(true);

  // Debug logs
  useEffect(() => {
    console.log('🧪 Users page - Auth state:', { 
      authLoading, 
      hasUser: !!user, 
      userId: user?.id,
      isAdmin: userRole.isAdmin,
      userRole 
    });
  }, [authLoading, user, userRole]);

  // ✅ Updated fetch function that waits for auth
  const fetchPelates = useCallback(async () => {
    // Don't fetch data until auth is loaded
    if (authLoading || !user) {
      console.log('⏳ Waiting for auth to load...', { authLoading, hasUser: !!user });
      return;
    }

    console.log('🚀 Auth loaded, fetching pelates data...');
    setDataLoading(true);

    try {
      const { data, error } = await supabase
        .from('pelates')
        .select(`
          *,
          active_subscriptions (subscription_status),
          pelates_tags (
            tags (
              id,
              name,
              color,
              created_at
            )
          )
        `);

      if (error) {
        console.error('Error fetching pelates:', error);
        toast.error('Failed to fetch pelates data');
        return;
      }

      console.log('✅ Pelates data fetched:', data?.length, 'records');

      // Fetch roles for users with auth_user_id
      if (data) {
        const pelatesWithAuth = data.filter(p => p.auth_user_id);
        console.log('🔍 Fetching roles for', pelatesWithAuth.length, 'users with auth');

        if (pelatesWithAuth.length > 0) {
          const { data: rolesData, error: rolesError } = await supabase
            .from('user_roles')
            .select(`
              auth_user_id,
              roles (
                name
              )
            `)
            .in('auth_user_id', pelatesWithAuth.map(p => p.auth_user_id!));

          if (rolesError) {
            console.error('Error fetching roles:', rolesError);
          } else {
            console.log('✅ Roles data fetched:', rolesData?.length, 'records');
          }

          // Merge roles data with pelates
          const enrichedData = data.map(pelati => ({
            ...pelati,
            user_roles: rolesData?.filter(r => r.auth_user_id === pelati.auth_user_id) || []
          }));

          setPelates(enrichedData as PelatesWithTags[]);
        } else {
          setPelates(data as PelatesWithTags[]);
        }
      }
    } catch (error) {
      console.error('Error in fetchPelates:', error);
      toast.error('Failed to fetch data');
    } finally {
      setDataLoading(false);
    }
  }, [supabase, authLoading, user]);

  // ✅ Effect that waits for auth before fetching data
  useEffect(() => {
    if (!authLoading) {
      console.log('🎯 Auth loading complete, triggering data fetch');
      fetchPelates();
    }
  }, [fetchPelates, authLoading]);

  // Existing effects
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (sidebarRef && !sidebarRef.contains(event.target as Node)) {
        closeSidebar();
      }
    }
  
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [sidebarRef]);

  useEffect(() => {
    function handleKeyPress(event: KeyboardEvent) {
      if (event.key === 'Escape' && (selectedPelatis || isAddingNew)) {
        closeSidebar();
      }
    }
  
    document.addEventListener('keydown', handleKeyPress);
    return () => {
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, [selectedPelatis, isAddingNew]);

  const sortPelates = useCallback((data: PelatesWithTags[]) => {
    return [...data].sort((a, b) => {
      if (sortKey === 'created_at') {
        const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
        const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
        return sortAsc 
          ? dateA.getTime() - dateB.getTime()
          : dateB.getTime() - dateA.getTime();
      }
      
      // Handle subscription_status specially
      if (sortKey === 'subscription_status') {
        const statusA = a.active_subscriptions?.[0]?.subscription_status ?? '';
        const statusB = b.active_subscriptions?.[0]?.subscription_status ?? '';
        if (statusA < statusB) return sortAsc ? -1 : 1;
        if (statusA > statusB) return sortAsc ? 1 : -1;
        return 0;
      }
      
      // Handle potentially null values for other fields
      const valueA = a[sortKey as keyof PelatesWithTags] ?? '';
      const valueB = b[sortKey as keyof PelatesWithTags] ?? '';
      
      if (valueA < valueB) return sortAsc ? -1 : 1;
      if (valueA > valueB) return sortAsc ? 1 : -1;
      return 0;
    });
  }, [sortKey, sortAsc]);

  const filteredAndSortedPelates = useMemo(() => {
    const filtered = pelates.filter(pelatis => {
      // Text search filter
      const matchesSearch = 
        pelatis.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pelatis.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        pelatis.last_name?.toLowerCase().includes(searchTerm.toLowerCase());

      // Tag filter - Changed from AND to OR logic
      const matchesTags = selectedFilterTags.length === 0 || 
        selectedFilterTags.some(filterTag =>  // Changed from every to some
          pelatis.pelates_tags?.some(pt => 
            pt.tags.id === filterTag.id
          )
        );

      return matchesSearch && matchesTags;
    });
    
    return sortPelates(filtered);
  }, [searchTerm, pelates, sortPelates, selectedFilterTags]);

  const handleSort = (key: SortKey) => {
    if (key === sortKey) {
      setSortAsc(!sortAsc);
    } else {
      setSortKey(key);
      setSortAsc(true);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => {
      const newData = { ...prev, [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value };
      
      if (!newData.client_name || name === 'name' || name === 'last_name') {
        newData.client_name = `${newData.name || ''} ${newData.last_name || ''}`.trim();
      }
      
      return newData;
    });
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    const now = new Date().toISOString();
    
    try {
      const clientName = formData.client_name || 
        `${formData.name || ''} ${formData.last_name || ''}`.trim();
  
      // Create only pelates record
      const pelatesData: Database['public']['Tables']['pelates']['Insert'] = {
        name: formData.name || null,
        last_name: formData.last_name || null,
        client_name: clientName || null,
        email: formData.email || null,
        phone: formData.phone || null,
        instagram: formData.instagram || null,
        created_at: now,
        updated_at: now
      };
  
      const { error } = await supabase
        .from('pelates')
        .insert([pelatesData])
        .select();
      
      if (error) throw error;
      
      toast.success('New pelatis added successfully!');
      await fetchPelates();
      closeSidebar();
      setFormData({});
      
    } catch (error) {
      console.error('Error creating pelatis:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create new pelatis');
    } finally {
      setIsSubmitting(false);
    }
  };

  const openSidebar = useCallback((pelatis?: Pelates) => {
    if (pelatis) {
      setSelectedPelatis(pelatis);
      setFormData(pelatis);
    } else {
      setIsAddingNew(true);
      setFormData({
        created_at: new Date().toISOString()
      });
    }
  }, []);

  const closeSidebar = useCallback(() => {
    setSelectedPelatis(null);
    setIsAddingNew(false);
    setFormData({});
  }, []);

  const formatDate = (dateString: string | null): string => {
    if (!dateString) return '-';  // Return placeholder for null dates
    const date = new Date(dateString);
    return `${date.getDate().toString().padStart(2, '0')}/${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getFullYear()}`;
  };

  const SortableHeader: React.FC<{ column: SortKey; label: string }> = ({ column, label }) => (
    <TableHead className="cursor-pointer" onClick={() => handleSort(column)}>
      <div className="flex items-center">
        {label}
        <ArrowUpDown className="ml-2 h-4 w-4" />
      </div>
    </TableHead>
  );

  // ✅ Show loading states
  if (authLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading authentication...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-center">
          <p className="text-red-600">Not authenticated. Please sign in.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-gray-100 overflow-hidden">
      <div className="flex-1 p-10 overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Pelates</h1>
          <div className="flex items-center space-x-4">
            {selectedClients.length > 0 && (
              <Button
                onClick={() => setShowEmailComposer(true)}
                variant="secondary"
              >
                <Mail className="mr-2 h-4 w-4" />
                Email Selected ({selectedClients.length})
              </Button>
            )}
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="Search pelates..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <TagFilter 
              onTagsChange={setSelectedFilterTags}
              className="min-w-[200px]"
            />
            <Button onClick={() => openSidebar()}>
              <PlusCircle className="mr-2 h-4 w-4" /> Add New
            </Button>
          </div>
        </div>

        {dataLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading data...</p>
            </div>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <input
                    type="checkbox"
                    checked={
                      selectedClients.length > 0 &&
                      selectedClients.length === filteredAndSortedPelates.length
                    }
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedClients(filteredAndSortedPelates);
                      } else {
                        setSelectedClients([]);
                      }
                    }}
                    className="w-4 h-4"
                  />
                </TableHead>
                <TableHead>#</TableHead>
                <SortableHeader column="client_name" label="Client Name" />
                <SortableHeader column="email" label="Email" />
                <SortableHeader column="phone" label="Phone" />
                <TableHead>Role</TableHead>
                <SortableHeader column="subscription_status" label="Subscription Status" />
                <TableHead>Tags</TableHead>
                <SortableHeader column="created_at" label="Created At" />
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedPelates.map((pelatis, index) => (
                <TableRow 
                  key={pelatis.id}
                  className="hover:bg-gray-100"
                >
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedClients.some(c => c.id === pelatis.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedClients([...selectedClients, pelatis]);
                        } else {
                          setSelectedClients(selectedClients.filter(c => c.id !== pelatis.id));
                        }
                      }}
                      className="w-4 h-4"
                    />
                  </TableCell>
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="flex-1">{pelatis.client_name || '-'}</span>
                      <Link href={`/admin/users/${pelatis.id}`} passHref>
                        <Button
                          variant="ghost"
                          size="sm"
                        >
                          <Eye className="h-4 w-4 text-green-500" />
                        </Button>
                      </Link>
                    </div>
                  </TableCell>
                  <TableCell>{pelatis.email || '-'}</TableCell>
                  <TableCell>{pelatis.phone || '-'}</TableCell>
                  <TableCell>
                    {pelatis.user_roles?.[0]?.roles?.name || '-'}
                  </TableCell>
                  <TableCell>
                    {pelatis.active_subscriptions?.[0]?.subscription_status ?? '-'}
                  </TableCell>
                  <TableCell>
                    <TagManager 
                      pelateId={pelatis.id}
                      onTagsChange={fetchPelates}
                      className="min-w-[200px]"
                    />
                  </TableCell>
                  <TableCell>{formatDate(pelatis.created_at)}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingPelatis(pelatis)}
                      >
                        <PencilIcon className="h-4 w-4 text-blue-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </div>

      {(selectedPelatis || isAddingNew) && (
        <div 
          ref={setSidebarRef}
          className="w-1/3 bg-white p-6 shadow-lg overflow-y-auto fixed right-0 top-0 bottom-0"
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold mb-4">
              {isAddingNew ? 'Add New Pelatis' : 'Edit Pelatis'}
            </h2>
            <Button
              variant="ghost"
              size="sm"
              onClick={closeSidebar}
              className="hover:bg-gray-100 rounded-full"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">First Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name || ''}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <Label htmlFor="last_name">Last Name</Label>
                <Input
                  id="last_name"
                  name="last_name"
                  value={formData.last_name || ''}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <Label htmlFor="client_name">Client Name</Label>
                <Input
                  id="client_name"
                  name="client_name"
                  value={formData.client_name || ''}
                  onChange={handleInputChange}
                  placeholder="Will be auto-filled if left empty"
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email || ''}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formData.phone || ''}
                  onChange={handleInputChange}
                />
              </div>
              <div>
                <Label htmlFor="instagram">Instagram</Label>
                <Input
                  id="instagram"
                  name="instagram"
                  value={formData.instagram || ''}
                  onChange={handleInputChange}
                  placeholder="@username"
                />
              </div>
              <div>
                <Label htmlFor="subscription_status">Subscription Status</Label>
                <Input
                  id="subscription_status"
                  name="subscription_status"
                  value={formData.subscription_status || ''}
                  onChange={handleInputChange}
                  disabled
                />
              </div>
              <div>
                <Label htmlFor="created_at">Created At</Label>
                <Input
                  id="created_at"
                  name="created_at"
                  type="date"
                  value={formData.created_at ? new Date(formData.created_at).toISOString().split('T')[0] : ''}
                  onChange={handleInputChange}
                />
              </div>
            </div>
            <div className="mt-6 flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={closeSidebar} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Submitting...' : (isAddingNew ? 'Add' : 'Update')}
              </Button>
            </div>
          </form>
        </div>
      )}

      {editingPelatis && (
        <PelatesEdit
          pelatis={editingPelatis}
          onClose={() => setEditingPelatis(null)}
          onSuccess={fetchPelates}
        />
      )}

      {showEmailComposer && (
        <EmailComposer
          selectedClients={selectedClients
            .filter(client => client.email !== null && client.email.trim() !== '')
            .map(client => ({
              email: client.email as string, // We've filtered out nulls
              client_name: client.client_name || 'Unnamed Client'
            }))}
          onClose={() => setShowEmailComposer(false)}
        />
      )}
    </div>
  );
};

export default PelatesPage;