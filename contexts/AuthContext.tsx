// contexts/AuthContext.tsx - WITH DEBUG LOGS
'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { User, Session } from '@supabase/supabase-js'
import { supabaseClient } from '@/lib/supabase-client'
import type { Database } from '@/types/supabase'

// Debug logging
console.log('AuthContext: supabaseClient loaded:', !!supabaseClient)
console.log('AuthContext: supabaseClient.auth exists:', !!supabaseClient?.auth)

interface UserRole {
  isAdmin: boolean
  isPelatis: boolean
  roles: string[]
  pelatisId: string | null
}

interface AuthContextType {
  user: User | null
  session: Session | null
  userRole: UserRole
  loading: boolean
  signOut: () => Promise<void>
  refreshUserRole: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Circuit breaker for auth operations to prevent rate limiting
class AuthCircuitBreaker {
  private failures = 0
  private lastFailTime = 0
  private readonly maxFailures = 3
  private readonly resetTimeout = 30000 // 30 seconds

  canExecute(): boolean {
    if (this.failures >= this.maxFailures) {
      if (Date.now() - this.lastFailTime > this.resetTimeout) {
        this.reset()
        return true
      }
      console.warn('Auth circuit breaker activated - skipping request')
      return false
    }
    return true
  }

  onSuccess(): void {
    this.reset()
  }

  onFailure(): void {
    this.failures++
    this.lastFailTime = Date.now()
    console.warn(`Auth circuit breaker failure ${this.failures}/${this.maxFailures}`)
  }

  private reset(): void {
    this.failures = 0
    this.lastFailTime = 0
  }
}

const authCircuitBreaker = new AuthCircuitBreaker()

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [userRole, setUserRole] = useState<UserRole>({
    isAdmin: false,
    isPelatis: false,
    roles: [],
    pelatisId: null
  })
  const [loading, setLoading] = useState(true)

  const fetchUserRole = async (userId: string): Promise<UserRole> => {
    console.log('🔍 fetchUserRole called with userId:', userId)
    
    if (!authCircuitBreaker.canExecute()) {
      console.log('⚠️ Circuit breaker blocked role fetch')
      return { isAdmin: false, isPelatis: false, roles: [], pelatisId: null }
    }

    try {
      console.log('🚀 Making parallel requests for roles and pelatis...')
      
      // Test direct database query first
      console.log('🧪 Testing direct database query...')
      const { data: testRoles, error: testError } = await supabaseClient
        .from('user_roles')
        .select(`
          *,
          roles (
            name
          )
        `)
        .eq('auth_user_id', userId)

      console.log('🧪 Direct query result:', { testRoles, testError })

      // Parallel fetch for roles and pelatis data
      const [rolesResult, pelatesResult] = await Promise.allSettled([
        supabaseClient.rpc('getUserRoles', { p_user_id: userId }),
        supabaseClient.from('pelates').select('id').eq('auth_user_id', userId).maybeSingle()
      ])

      console.log('📊 Roles RPC result:', rolesResult)
      console.log('👤 Pelates result:', pelatesResult)

      let roles: string[] = []
      let pelatisId: string | null = null

      // Handle roles result
      if (rolesResult.status === 'fulfilled' && !rolesResult.value.error) {
        roles = Array.isArray(rolesResult.value.data) ? rolesResult.value.data : []
        console.log('✅ Roles from RPC:', roles)
      } else {
        console.error('❌ Roles RPC failed:', rolesResult)
        
        // Fallback: use direct query results
        if (testRoles && !testError) {
          roles = testRoles.map(r => r.roles?.name).filter(Boolean)
          console.log('🔄 Using fallback roles:', roles)
        }
      }

      // Handle pelatis result
      if (pelatesResult.status === 'fulfilled' && !pelatesResult.value.error && pelatesResult.value.data) {
        pelatisId = pelatesResult.value.data.id
        console.log('✅ Pelatis ID fetched:', pelatisId)
      } else {
        console.log('⚠️ Pelatis fetch result:', pelatesResult)
      }

      const result = {
        isAdmin: roles.includes('admin'),
        isPelatis: !!pelatisId,
        roles,
        pelatisId
      }

      console.log('🎯 Final user role result:', result)
      
      authCircuitBreaker.onSuccess()
      return result
    } catch (error) {
      console.error('💥 Error fetching user roles:', error)
      authCircuitBreaker.onFailure()
      return { isAdmin: false, isPelatis: false, roles: [], pelatisId: null }
    }
  }

  const refreshUserRole = async () => {
    if (user) {
      const roles = await fetchUserRole(user.id)
      setUserRole(roles)
    }
  }

  const signOut = async () => {
    try {
      await supabaseClient.auth.signOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  useEffect(() => {
    console.log('🎬 AuthContext useEffect started')
    let mounted = true

    // Validate supabaseClient before using
    if (!supabaseClient || !supabaseClient.auth) {
      console.error('💥 AuthContext: supabaseClient is not properly initialized')
      setLoading(false)
      return
    }

    console.log('✅ supabaseClient validation passed')

    // Get initial session
    const getInitialSession = async () => {
      console.log('🚀 getInitialSession started')
      try {
        console.log('📡 Calling supabaseClient.auth.getSession()...')
        
        // Add timeout to prevent hanging
        const sessionPromise = supabaseClient.auth.getSession()
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('getSession timeout after 10 seconds')), 10000)
        })
        
        const result = await Promise.race([sessionPromise, timeoutPromise]) as any
        const { data: { session: initialSession }, error } = result
        
        console.log('📡 getSession response:', { 
          hasSession: !!initialSession, 
          userId: initialSession?.user?.id,
          error: error?.message 
        })
        
        if (error) {
          console.error('❌ Error getting initial session:', error)
          if (mounted) {
            setLoading(false)
          }
          return
        }

        console.log('📱 Initial session details:', { 
          hasSession: !!initialSession, 
          userId: initialSession?.user?.id,
          email: initialSession?.user?.email
        })

        if (mounted) {
          console.log('✅ Setting session and user state')
          setSession(initialSession)
          setUser(initialSession?.user ?? null)
          
          if (initialSession?.user) {
            console.log('👤 User found, fetching roles for:', initialSession.user.id)
            const roles = await fetchUserRole(initialSession.user.id)
            console.log('🎯 Roles returned:', roles)
            if (mounted) {
              console.log('✅ Setting user roles:', roles)
              setUserRole(roles)
            }
          } else {
            console.log('❌ No user in initial session')
          }
          
          console.log('✅ Setting loading to false')
          setLoading(false)
        } else {
          console.log('⚠️ Component unmounted, skipping state updates')
        }
      } catch (error) {
        console.error('💥 Error in getInitialSession:', error)
        if (mounted) {
          console.log('⚠️ Setting loading to false due to error')
          setLoading(false)
        }
      }
    }

    console.log('🚀 Starting getInitialSession...')
    getInitialSession()

    console.log('🔗 Setting up auth state change listener...')
    // Set up auth state change listener - ONLY ONE for the entire app
    const { data: { subscription } } = supabaseClient.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔄 Auth state changed:', event, 'userId:', session?.user?.id)
        
        if (!mounted) {
          console.log('⚠️ Component unmounted, ignoring auth change')
          return
        }

        setSession(session)
        setUser(session?.user ?? null)

        if (session?.user) {
          // Only fetch roles on significant auth events
          if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
            console.log('🔄 Fetching roles for auth event:', event, session.user.id)
            const roles = await fetchUserRole(session.user.id)
            if (mounted) {
              console.log('✅ Setting roles from auth change:', roles)
              setUserRole(roles)
            }
          }
        } else {
          console.log('❌ No user, resetting roles')
          // User signed out
          setUserRole({ isAdmin: false, isPelatis: false, roles: [], pelatisId: null })
        }

        if (mounted) {
          setLoading(false)
        }
      }
    )

    // Cleanup function
    return () => {
      console.log('🧹 AuthContext cleanup - unmounting')
      mounted = false
      subscription.unsubscribe()
    }
  }, []) // Empty dependency array - this effect should only run once

  const value: AuthContextType = {
    user,
    session,
    userRole,
    loading,
    signOut,
    refreshUserRole,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}