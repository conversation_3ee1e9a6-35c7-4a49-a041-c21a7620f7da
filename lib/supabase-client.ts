// lib/supabase-client.ts
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import type { Database } from '@/types/supabase' // ✅ Fix: should be 'supabase' not 'database'

// ✅ Single client instance - created once and reused everywhere
let supabaseClientInstance: ReturnType<typeof createClientComponentClient<Database>> | null = null

export const getSupabaseClient = () => {
  if (!supabaseClientInstance) {
    supabaseClientInstance = createClientComponentClient<Database>()
  }
  return supabaseClientInstance
}

// ✅ Export as 'supabaseClient' (this is what your imports expect)
export const supabaseClient = getSupabaseClient()

// ✅ Also export as 'supabase' for compatibility
export const supabase = getSupabaseClient()

// Server components can still create their own clients (that's fine)
export { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
export { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
export { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'