# 📋 User Pages → Components Mapping for Server-First Auth

## 🔴 **High Priority - Client-Side Pages + Components**

### 1. **`app/user/bookings/page.tsx`** (Client-Side)
**Current Pattern:** `'use client'` with useEffect auth
**Components Used:**
- `@/components/ui/card` ✅
- `@/components/ui/button` ✅
- Direct Supabase client usage
**Conversion:** Convert to server component + client component for interactivity

### 2. **`app/user/strength-program/page.tsx`** (Client-Side)
**Current Pattern:** Large client component with complex auth logic
**Components Used:**
- `@/hooks/useSupabase` 🔄 (needs update)
- `@/hooks/useStrengthProgram` 🔄 (needs update)
- `@/components/ui/card` ✅
- `@/components/ui/button` ✅
- `@/components/startingStrength/PlateCalculator` ✅
- `@/components/startingStrength/WarmupSets` ✅
- `@/components/startingStrength/WorkoutPlateGuide` ✅
- `@/components/startingStrength/WorkoutCompletionForm` ✅
**Conversion:** Major refactor - move auth to server, pass data as props

### 3. **`app/user/wods/page.tsx`** (Client-Side)
**Current Pattern:** Client-side with manual auth checking
**Components Used:**
- `createClientComponentClient` 🔄 (needs server data)
- `@/components/ui/card` ✅
- ReactMarkdown ✅
**Conversion:** Convert to server component + client component for swipe functionality

### 4. **`app/user/wods/today/page.tsx`** (Need to Check)
**Components Used:** TBD
**Conversion:** TBD

## 🟡 **Medium Priority - Mixed Pattern Pages + Components**

### 5. **`app/user/goals/page.tsx`** (Server Component)
**Current Pattern:** Server auth but minimal data fetching
**Components Used:**
- `./client` → `components/goals_badges/GoalsManager.tsx` 🔄
**Related Components:**
- `components/goals_badges/GoalsManager.tsx` - Uses `useSupabase` 🔄
- `components/goals_badges/GoalCard.tsx` 🔄
- `components/goals_badges/GoalForm.tsx` 🔄
**Conversion:** Update client component to accept server data

### 6. **`app/user/exercise-records/page.tsx`** (Server Component)
**Current Pattern:** Good server-first pattern
**Components Used:**
- `./client` → `components/exercise-records/ExerciseRecordsClient.tsx` 🔄
- `./QuickAddForm.tsx` ✅
**Conversion:** Minor updates to use new auth utility

### 7. **`app/user/achievements/page.tsx`** (Server Component)
**Current Pattern:** Good server-first pattern
**Components Used:**
- `./client` → `components/achievements/AchievementsClient.tsx` 🔄
**Conversion:** Minor updates to use new auth utility

### 8. **`app/user/profile/page.tsx`** (Server Component)
**Current Pattern:** Good server-first pattern
**Components Used:**
- `./ProfileContent.tsx` 🔄
- `@/components/ui/card` ✅
**Related Components:**
- `components/users/PelatesEdit.tsx` 🔄
**Conversion:** Minor updates to use new auth utility

### 9. **`app/user/profile/edit/page.tsx`** (Need to Check)
**Components Used:**
- `./ProfileEditForm.tsx` 🔄
**Conversion:** TBD

### 10. **`app/user/profile/onboarding/page.tsx`** (Need to Check)
**Components Used:** TBD
**Conversion:** TBD

## 🟢 **Low Priority - Already Good Pattern**

### 11. **`app/user/front-page/page.tsx`** (Server Component)
**Current Pattern:** Excellent server-first pattern
**Components Used:**
- `./UserDashboard.tsx` 🔄 (may need minor updates)
- `./AdminDashboard.tsx` 🔄 (may need minor updates)
**Conversion:** Minor updates to use new auth utility

## 📊 **Pages Needing Investigation**

### 12. **`app/user/fitness-assesment/page.tsx`**
**Components Used:**
- `./components/FitnessAssessmentForm.tsx` 🔄
- `./components/FitnessAssessmentResults.tsx` 🔄
- `./components/WeightLossSlider.tsx` ✅
- `./components/DebugView.tsx` ✅

### 13. **`app/user/injury-report/page.tsx`**
**Components Used:**
- `./InjuryReportForm.tsx` 🔄

### 14. **`app/user/notifications/page.tsx`**
**Components Used:**
- `@/components/NotificationsPopover.tsx` 🔄 (uses useNotifications)

### 15. **`app/user/session-book/page.tsx`**
**Components Used:** TBD

### 16. **`app/user/sessions/bookings/page.tsx`**
**Components Used:** TBD

### 17. **`app/user/support/page.tsx`**
**Components Used:**
- `./CreateThreadButton.tsx` 🔄
- `./SupportTable.tsx` 🔄
- `./SupportThreads.tsx` 🔄

### 18. **`app/user/support/[threadId]/page.tsx`**
**Components Used:**
- `./ReplyForm.tsx` 🔄

### 19. **`app/user/weight-tracker/page.tsx`**
**Components Used:** TBD

## 🎯 **Component Categories**

### **🔄 Components That Need Updates:**
- **Auth-dependent components** - Accept auth props instead of using hooks
- **Data-fetching components** - Accept initialData props from server
- **Form components** - May need auth context for submissions

### **✅ Components That Stay the Same:**
- **Pure UI components** - No auth dependencies
- **Utility components** - No data fetching
- **Static components** - No dynamic behavior

## 🚀 **Conversion Priority Order**

### **Week 1: Foundation**
1. `app/user/bookings/page.tsx` + UI components
2. `app/user/wods/page.tsx` + ReactMarkdown integration
3. Update `components/goals_badges/GoalsManager.tsx`

### **Week 2: Complex Pages**
1. `app/user/strength-program/page.tsx` + all strength components
2. `app/user/goals/page.tsx` + goals components
3. `app/user/exercise-records/page.tsx` + exercise components

### **Week 3: Remaining Pages**
1. All fitness assessment, injury report, support pages
2. Profile and notification pages
3. Session booking and weight tracker pages

## 📝 **Notes**

- **🔄** = Needs updates for server-first pattern
- **✅** = Can stay as-is (no auth dependencies)
- **TBD** = Needs investigation to determine current pattern

Each page conversion involves:
1. **Server Component**: Handle auth + data fetching
2. **Client Component**: Handle interactivity with passed props
3. **Related Components**: Update to accept props instead of using auth hooks

## 📋 **Detailed Component Dependencies**

### **Strength Program Components** (High Impact)
- `@/components/startingStrength/PlateCalculator.tsx` ✅ - Pure calculation component
- `@/components/startingStrength/WarmupSets.tsx` ✅ - Pure display component
- `@/components/startingStrength/WorkoutPlateGuide.tsx` ✅ - Pure display component
- `@/components/startingStrength/WorkoutCompletionForm.tsx` 🔄 - May use auth for submissions
- `@/hooks/useStrengthProgram.ts` 🔄 - Needs major refactor for server-first

### **Goals & Badges Components** (Medium Impact)
- `@/components/goals_badges/GoalsManager.tsx` 🔄 - Uses useSupabase hook
- `@/components/goals_badges/GoalCard.tsx` 🔄 - May have auth-dependent actions
- `@/components/goals_badges/GoalForm.tsx` 🔄 - Form submissions need auth
- `@/components/goals_badges/BadgeDisplay.tsx` ✅ - Pure display component
- `@/components/goals_badges/UserGoalsManager.tsx` 🔄 - User-specific data fetching

### **Exercise Components** (Medium Impact)
- `@/components/exercises/ExercisesClient.tsx` 🔄 - Uses createClientComponentClient
- `@/components/exercises/ExerciseTable.tsx` ✅ - Pure table component
- `@/components/exercises/ExerciseForm.tsx` 🔄 - Form submissions need auth
- `@/components/exercises/MediaPreviewDialog.tsx` ✅ - Pure UI component

### **WOD Components** (Medium Impact)
- `@/components/wods/WodPageClient.tsx` 🔄 - Uses createClientComponentClient
- `@/components/wods/UserWODs.tsx` 🔄 - User-specific WOD data
- `@/components/wods/WodForm.tsx` 🔄 - Admin form component

### **Support Components** (Low Impact)
- `@/components/support/CreateThreadButton.tsx` 🔄 - Creates support tickets
- `@/components/support/SupportTable.tsx` 🔄 - Displays user tickets
- `@/components/support/ReplyForm.tsx` 🔄 - Reply submissions

### **Profile Components** (Low Impact)
- `@/components/users/PelatesEdit.tsx` 🔄 - User profile editing
- `@/components/profile/ProfileContent.tsx` 🔄 - Profile display
- `@/components/profile/ProfileEditForm.tsx` 🔄 - Profile form

## 🔧 **Hook Dependencies That Affect Components**

### **Primary Hooks Needing Updates:**
1. `@/hooks/useSupabase.ts` 🔄 - Used by 15+ components
2. `@/hooks/useAuth.ts` (from AuthContext) 🔄 - Used by 10+ components
3. `@/hooks/useNotifications.ts` 🔄 - Used by NotificationsPopover
4. `@/hooks/useStrengthProgram.ts` 🔄 - Used by strength program pages
5. `@/hooks/useUserRole.ts` 🔄 - Used for role checking

### **Components Affected by Hook Changes:**
- **useSupabase dependents**: All goals, exercises, wods, support components
- **useAuth dependents**: Layout components, navigation, user menus
- **useNotifications dependents**: Header, notification popover
- **useStrengthProgram dependents**: All strength program components

## 🎯 **Conversion Patterns by Component Type**

### **Data Display Components** → Accept `initialData` prop
```typescript
// Before
const { data, loading } = useSupabase().fetchData()

// After
interface Props { initialData: Data[] }
const [data, setData] = useState(initialData)
```

### **Form Components** → Accept `onSubmit` callback prop
```typescript
// Before
const { supabase } = useSupabase()
const handleSubmit = () => supabase.from('table').insert(data)

// After
interface Props { onSubmit: (data: FormData) => Promise<void> }
const handleSubmit = () => onSubmit(formData)
```

### **Auth-Dependent Components** → Accept `userAuth` prop
```typescript
// Before
const { user, isAdmin } = useAuth()

// After
interface Props { userAuth: ServerAuth }
const { user, isAdmin } = userAuth
```
