High Priority - Core Auth Components (Need Major Updates)
components/AuthForm.tsx ✅ - Login form (can stay client-side, but may need updates)
components/SignOutButton.tsx ✅ - Sign out functionality (can stay client-side)
components/NotificationsPopover.tsx - Uses useNotifications hook with client auth
components/layout/MainHeader.tsx - Uses session props but may need auth context updates
components/layout/MainContent.tsx - Manages layout based on auth state
components/layout/AdminSidebar.tsx - Admin navigation (need to check)
components/layout/SecondaryNavigation.tsx - Navigation based on auth (need to check)
🟡 Medium Priority - Data-Fetching Components (Need Auth Pattern Updates)
components/exercises/ExercisesClient.tsx - Uses createClientComponentClient
components/wods/WodPageClient.tsx - Uses createClientComponentClient
components/admin/AdminNavigationDrawer.tsx - Admin component (need to check)
components/admin/CommandPalette.tsx - Admin component (need to check)
components/mydata/MyDataSettingsClient.tsx - Uses createClientComponentClient
components/goals_badges/GoalsManager.tsx - Uses useSupabase hook
components/goals_badges/AdminBadgeManagement.tsx - Uses useSupabase hook
components/goals_badges/admin/AssignmentManagement.tsx - Uses useSupabase hook
components/goals_badges/admin/BadgeManagement.tsx - Uses useSupabase hook
components/users/PelatesEdit.tsx - User management component (need to check)
components/kpis/KpiDashboard.tsx - Dashboard component (need to check)
components/finances/*** - All finance components (need to check auth patterns)
components/invoices/*** - All invoice components (need to check auth patterns)
components/merchandise/*** - All merchandise components (need to check auth patterns)
🟢 Low Priority - Utility Components (Minor Updates)
components/EmailComposer.tsx - May use auth for sending emails
components/UserMenu.tsx - User menu component (need to check)
components/AdminActions.tsx - Admin actions (need to check)
components/AdminDebug.tsx - Debug component (need to check)
components/tags/TagManager.tsx - Tag management (need to check)
components/tags/TagFilter.tsx - Tag filtering (need to check)
🔵 Context & Provider Components (Architecture Updates)
contexts/AuthContext.tsx ⚠️ - CRITICAL - Main auth context (needs major refactor)
components/Providers.tsx - App providers wrapper (need to check)
🟠 Hook-Dependent Components (Indirect Updates)
Components that use these hooks will need updates when hooks are refactored:

hooks/useSupabase.ts - Used by many components
hooks/useNotifications.ts - Used by NotificationsPopover
hooks/useUserRole.ts - Used for role checking
🎯 Component Update Strategy
Phase 1: Core Infrastructure (Week 1)
Update contexts/AuthContext.tsx - Make it lightweight, server-first compatible
Update components/layout/MainHeader.tsx - Accept server auth props
Update components/layout/MainContent.tsx - Accept server auth props
Update components/NotificationsPopover.tsx - Accept userId prop instead of using hooks
Phase 2: Data Components (Week 2)
Convert data-fetching components to accept initialData props
Update admin components to use server-passed auth state
Refactor finance/invoice components for server-first pattern
Phase 3: Specialized Components (Week 3)
Update goals/badges components to accept server data
Convert merchandise components to server-first
Update exercise/wod components for better performance
Component Conversion Pattern
Before (Client-Side):
'use client'
export function MyComponent() {
  const { user, loading } = useAuth()
  const { supabase } = useSupabase()
  
  useEffect(() => {
    if (user) fetchData()
  }, [user])
  
  if (loading) return <Loading />
  // ...
}
  After (Server-First Compatible):
  'use client'
interface MyComponentProps {
  initialData: MyData[]
  userAuth: ServerAuth
}

export function MyComponent({ initialData, userAuth }: MyComponentProps) {
  const [data, setData] = useState(initialData)
  // No loading states needed for initial data
  // Use userAuth for permissions/display logic
  // ...
}
  The key principle is: Components receive data and auth state as props from server components, rather than fetching it themselves.


  Admin Pages → Components Mapping Summary
🔴 Critical Priority
app/admin/layout.tsx - CRITICAL - Client-side layout affecting ALL admin pages
app/admin/users/view/page.tsx - Major user management page with complex auth logic
app/admin/reports/kpis/page.tsx - KPI dashboard with analytics components
🟡 Medium Priority (Mixed Patterns)
app/admin/finances/page.tsx - Financial dashboard (good server pattern)
app/admin/wods/page.tsx - WOD management (excellent server pattern)
app/admin/auth-debug/page.tsx - Auth debugging tools
📊 Pages Needing Investigation (50+ pages)
Reports & Analytics - Check-ins, dashboards, new members
Session Management - Calendar, daily view, creation
Member Management - Subscriptions, individual users
Financial Management - Expenses, payments, invoices
Content Management - WODs, exercises, analytics
Communication - Support, notifications, email templates
System Tools - Settings, check-ins, merchandise, MyData
Key Component Dependencies Identified:
🔄 Critical Components (Affect All Admin Pages):
app/admin/layout.tsx - Client-side layout
@/components/layout/AdminSidebar.tsx - Admin navigation
@/components/admin/AdminNavigationDrawer.tsx - Drawer navigation
@/contexts/AuthContext.tsx - Auth context
🔄 High-Impact Component Categories:
Financial Components - Cashflow, expenses, transfers, reconciliation
Invoice Components - Management, tables, bulk actions
KPI & Analytics Components - Dashboards, charts, business metrics
User Management Components - User editing, tags, email composer
🔄 Critical Hooks Needing Updates:
@/hooks/useSupabase.ts - Used by 20+ admin components
@/contexts/AuthContext.tsx - Used by admin layout and navigation
@/hooks/useKpiData.tsx - KPI dashboard functionality
Admin-specific hooks - Account balances, transfers, merchandise, invoices
🚀 Admin Conversion Strategy:
Week 1: Critical Infrastructure
Fix app/admin/layout.tsx (affects ALL admin pages)
Update app/admin/users/view/page.tsx
Update @/contexts/AuthContext.tsx
Update admin navigation components
Week 2-4: Progressive Page Conversion
High-traffic pages (KPIs, finances, WODs)
Data management pages (invoices, exercises, subscriptions)
Secondary features (support, merchandise, system tools)
Key Differences from User Pages:
Layout Impact - Client-side layout affects ALL admin pages
Higher Security - All need admin role verification
Complex Data - More relationships and calculations
Real-time Updates - Dashboards and analytics
Bulk Operations - User and data management
The admin section is more complex than user pages due to the client-side layout affecting all pages and the higher complexity of admin functionality. The layout conversion is the critical first step that will enable all other admin page conversions.