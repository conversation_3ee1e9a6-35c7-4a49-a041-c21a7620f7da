Complete List of Admin Pages
🔴 High Priority - Client-Side Only Pages (Need Complete Conversion)
app/admin/layout.tsx - Currently client-side layout, needs server auth
app/admin/users/view/page.tsx - Large client component with complex auth logic
app/admin/reports/kpis/page.tsx - Client-side only
app/admin/auth-debug/page.tsx - Need to check pattern
app/admin/check-ins/page.tsx - Need to check pattern
app/admin/check-role/page.tsx - Need to check pattern
app/admin/crossfitimer/page.tsx - Need to check pattern
app/admin/daily/page.tsx - Need to check pattern
app/admin/diagrams/page.tsx - Need to check pattern
app/admin/email-templates/page.tsx - Need to check pattern
app/admin/exercise-analytics/page.tsx - Need to check pattern
app/admin/exercise-records/page.tsx - Need to check pattern
app/admin/exercises/consolidate/page.tsx - Need to check pattern
app/admin/expenses/page.tsx - Need to check pattern
app/admin/goals/page.tsx - Need to check pattern
app/admin/invoices/page.tsx - Need to check pattern
app/admin/invoices/[id]/page.tsx - Need to check pattern
app/admin/invoices/view/[id]/page.tsx - Need to check pattern
app/admin/marketing/page.tsx - Need to check pattern
app/admin/merchandise/page.tsx - Need to check pattern
app/admin/mydata/logs/page.tsx - Need to check pattern
app/admin/mydata/settings/page.tsx - Need to check pattern
app/admin/mydata/test/page.tsx - Need to check pattern
app/admin/notifications/page.tsx - Need to check pattern
app/admin/notifications/dashboard/page.tsx - Need to check pattern
app/admin/notifications/subscriptions/page.tsx - Need to check pattern
app/admin/payments/add/page.tsx - Need to check pattern
app/admin/payments/view/page.tsx - Need to check pattern
app/admin/reports/checkins/page.tsx - Need to check pattern
app/admin/reports/dashboard/page.tsx - Need to check pattern
app/admin/reports/new-members/page.tsx - Need to check pattern
app/admin/reports/page.tsx - Need to check pattern
app/admin/sessions/add/page.tsx - Need to check pattern
app/admin/sessions/calendar/page.tsx - Need to check pattern
app/admin/sessions/day/page.tsx - Need to check pattern
app/admin/settings/page.tsx - Need to check pattern
app/admin/subscriptions/active/page.tsx - Need to check pattern
app/admin/subscriptions/expiring/page.tsx - Need to check pattern
app/admin/support/page.tsx - Need to check pattern
app/admin/support/[threadId]/page.tsx - Need to check pattern
app/admin/users/[id]/page.tsx - Need to check pattern
app/admin/weekly-bookings/page.tsx - Need to check pattern
app/admin/wods/add/page.tsx - Need to check pattern
app/admin/wods/back/page.tsx - Need to check pattern
app/admin/wods/conversiontable/page.tsx - Need to check pattern
app/admin/wods/details/page.tsx - Need to check pattern
app/admin/wods/edit/[id]/page.tsx - Need to check pattern
app/admin/wods/exercises/page.tsx - Need to check pattern
app/admin/wods/print/page.tsx - Need to check pattern
🟡 Medium Priority - Mixed Pattern Pages (Need Auth Utility Updates)
app/admin/finances/page.tsx ✅ - Server-first but needs getServerAuth() utility
app/admin/finances/accounts/page.tsx - Need to check pattern
app/admin/finances/analysis/page.tsx - Need to check pattern
app/admin/finances/cashflow/page.tsx - Need to check pattern
app/admin/finances/reconciliation/page.tsx - Need to check pattern
app/admin/finances/transactions/page.tsx - Need to check pattern
app/admin/finances/transfers/page.tsx - Need to check pattern
app/admin/finances/winbank/page.tsx - Need to check pattern
🟢 Low Priority - Already Good Pattern (Minor Updates)
app/admin/wods/page.tsx ✅ - Already server-first, may need admin auth check
🎯 Key Observations
Layout Issue: The app/admin/layout.tsx is client-side, which means all admin pages inherit client-side behavior
Mixed Patterns: Some pages use server-first (like finances, wods) while others are fully client-side
Auth Checking: Most pages need proper admin role verification using the new getServerAuth() pattern
Data Fetching: Many client-side pages do manual data fetching that should move to server

Conversion Strategy for Admin Pages
Each admin page should follow this pattern:
// Standard admin server-first pattern
export default async function AdminPage() {
  const auth = await getServerAuth()
  
  if (!auth.isAdmin) {
    redirect('/unauthorized')
  }
  
  // Fetch admin-specific data with proper filtering
  const adminData = await fetchAdminData(auth)
  
  return <AdminPageClient initialData={adminData} userAuth={auth} />
}
Priority Order:

Fix layout first - Convert app/admin/layout.tsx to server-first
High-traffic pages - users/view, reports/kpis, finances pages
Core functionality - sessions, wods, payments
Secondary features - notifications, merchandise, etc.
