📋 User Pages That Must Change to Server-First
🔴 High Priority - Client-Side Only Pages
These pages are currently using 'use client' and need complete conversion:

 app/user/bookings/page.tsx - Currently client-side with useEffect auth
 app/user/strength-program/page.tsx - Large client component with complex auth logic
 app/user/wods/page.tsx - Client-side with manual auth checking
 app/user/wods/today/page.tsx - Likely client-side (need to check)
🟡 Medium Priority - Mixed Pattern Pages
These pages use server components but don't follow the new standardized pattern:

app/user/goals/page.tsx - Uses old server pattern, needs auth data fetching
app/user/fitness-assesment/page.tsx - Need to check current pattern
app/user/injury-report/page.tsx - Need to check current pattern
app/user/lowbackpain/assessment/page.tsx - Need to check current pattern
app/user/notifications/page.tsx - Need to check current pattern
app/user/obese/exercise_program/page.tsx - Need to check current pattern
app/user/pricing/page.tsx - Need to check current pattern
app/user/profile/edit/page.tsx - Need to check current pattern
app/user/profile/onboarding/page.tsx - Need to check current pattern
 app/user/session-book/page.tsx - Need to check current pattern
 app/user/sessions/bookings/page.tsx - Need to check current pattern
 app/user/strength-program/admin/page.tsx - Need to check current pattern
 app/user/strength-program/custom-workout/page.tsx - Need to check current pattern
 app/user/strength-program/log/page.tsx - Need to check current pattern
 app/user/strength-program/next-workout/page.tsx - Need to check current pattern
 app/user/strength-program/schedule/page.tsx - Need to check current pattern
 app/user/strength-program/setup/page.tsx - Need to check current pattern
 app/user/support/page.tsx - Need to check current pattern
 app/user/support/[threadId]/page.tsx - Need to check current pattern
 app/user/weight-tracker/page.tsx - Need to check current pattern
🟢 Low Priority - Already Good Pattern
These pages already follow a good server-first pattern but may need minor updates:

 app/user/achievements/page.tsx ✅ - Already server-first, may need auth utility update
 app/user/exercise-records/page.tsx ✅ - Already server-first, may need auth utility update
 app/user/front-page/page.tsx ✅ - Already server-first, may need auth utility update
 app/user/profile/page.tsx ✅ - Already server-first, may need auth utility update


Conversion Strategy
According to the architecture document, each page should follow this pattern:

// Standard server-first pattern
export default async function UserPage() {
  const auth = await getServerAuth()
  
  if (!auth.user) redirect('/auth')
  
  const userData = await fetchUserData(auth)
  return <UserPageClient data={userData} auth={auth} />
}