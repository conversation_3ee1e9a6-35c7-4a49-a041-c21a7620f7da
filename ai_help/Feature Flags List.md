# 🚩 Feature Flags for Server-First Auth Migration

## 🎯 **Global Feature Flags**

```typescript
// utils/feature-flags.ts
export const FeatureFlags = {
  // Master switches
  SERVER_FIRST_AUTH: process.env.FEATURE_SERVER_FIRST_AUTH === 'true',
  SERVER_FIRST_USER_SECTION: process.env.FEATURE_SF_USER_SECTION === 'true',
  SERVER_FIRST_ADMIN_SECTION: process.env.FEATURE_SF_ADMIN_SECTION === 'true',

  // User page feature flags
  ...userPageFlags,

  // Admin page feature flags
  ...adminPageFlags,

  // Component feature flags
  ...componentFlags
}
```

## 👤 **User Page Feature Flags**

### **High Priority - Client-Side Pages (Need Complete Conversion)**
```typescript
const userPageFlags = {
  // 🔴 Critical conversions
  SERVER_FIRST_USER_BOOKINGS: process.env.FEATURE_SF_USER_BOOKINGS === 'true',
  SERVER_FIRST_USER_STRENGTH_PROGRAM: process.env.FEATURE_SF_USER_STRENGTH_PROGRAM === 'true',
  SERVER_FIRST_USER_WODS: process.env.FEATURE_SF_USER_WODS === 'true',
  SERVER_FIRST_USER_WODS_TODAY: process.env.FEATURE_SF_USER_WODS_TODAY === 'true',

  // 🟡 Medium priority conversions
  SERVER_FIRST_USER_GOALS: process.env.FEATURE_SF_USER_GOALS === 'true',
  SERVER_FIRST_USER_EXERCISE_RECORDS: process.env.FEATURE_SF_USER_EXERCISE_RECORDS === 'true',
  SERVER_FIRST_USER_ACHIEVEMENTS: process.env.FEATURE_SF_USER_ACHIEVEMENTS === 'true',
  SERVER_FIRST_USER_PROFILE: process.env.FEATURE_SF_USER_PROFILE === 'true',
  SERVER_FIRST_USER_PROFILE_EDIT: process.env.FEATURE_SF_USER_PROFILE_EDIT === 'true',
  SERVER_FIRST_USER_PROFILE_ONBOARDING: process.env.FEATURE_SF_USER_PROFILE_ONBOARDING === 'true',

  // 🟢 Low priority - already good patterns
  SERVER_FIRST_USER_FRONT_PAGE: process.env.FEATURE_SF_USER_FRONT_PAGE === 'true',

  // 📊 Pages needing investigation
  SERVER_FIRST_USER_FITNESS_ASSESSMENT: process.env.FEATURE_SF_USER_FITNESS_ASSESSMENT === 'true',
  SERVER_FIRST_USER_INJURY_REPORT: process.env.FEATURE_SF_USER_INJURY_REPORT === 'true',
  SERVER_FIRST_USER_LOWBACKPAIN_ASSESSMENT: process.env.FEATURE_SF_USER_LOWBACKPAIN_ASSESSMENT === 'true',
  SERVER_FIRST_USER_NOTIFICATIONS: process.env.FEATURE_SF_USER_NOTIFICATIONS === 'true',
  SERVER_FIRST_USER_OBESE_EXERCISE_PROGRAM: process.env.FEATURE_SF_USER_OBESE_EXERCISE_PROGRAM === 'true',
  SERVER_FIRST_USER_PRICING: process.env.FEATURE_SF_USER_PRICING === 'true',
  SERVER_FIRST_USER_SESSION_BOOK: process.env.FEATURE_SF_USER_SESSION_BOOK === 'true',
  SERVER_FIRST_USER_SESSIONS_BOOKINGS: process.env.FEATURE_SF_USER_SESSIONS_BOOKINGS === 'true',
  SERVER_FIRST_USER_SUPPORT: process.env.FEATURE_SF_USER_SUPPORT === 'true',
  SERVER_FIRST_USER_SUPPORT_THREAD: process.env.FEATURE_SF_USER_SUPPORT_THREAD === 'true',
  SERVER_FIRST_USER_WEIGHT_TRACKER: process.env.FEATURE_SF_USER_WEIGHT_TRACKER === 'true',

  // Strength program sub-pages
  SERVER_FIRST_USER_STRENGTH_ADMIN: process.env.FEATURE_SF_USER_STRENGTH_ADMIN === 'true',
  SERVER_FIRST_USER_STRENGTH_CUSTOM_WORKOUT: process.env.FEATURE_SF_USER_STRENGTH_CUSTOM_WORKOUT === 'true',
  SERVER_FIRST_USER_STRENGTH_LOG: process.env.FEATURE_SF_USER_STRENGTH_LOG === 'true',
  SERVER_FIRST_USER_STRENGTH_NEXT_WORKOUT: process.env.FEATURE_SF_USER_STRENGTH_NEXT_WORKOUT === 'true',
  SERVER_FIRST_USER_STRENGTH_SCHEDULE: process.env.FEATURE_SF_USER_STRENGTH_SCHEDULE === 'true',
  SERVER_FIRST_USER_STRENGTH_SETUP: process.env.FEATURE_SF_USER_STRENGTH_SETUP === 'true',
}
```

## 👨‍💼 **Admin Page Feature Flags**

### **Critical Priority - Layout & Core Infrastructure**
```typescript
const adminPageFlags = {
  // 🔴 CRITICAL - Affects ALL admin pages
  SERVER_FIRST_ADMIN_LAYOUT: process.env.FEATURE_SF_ADMIN_LAYOUT === 'true',

  // 🔴 High priority - Client-side pages
  SERVER_FIRST_ADMIN_USERS_VIEW: process.env.FEATURE_SF_ADMIN_USERS_VIEW === 'true',
  SERVER_FIRST_ADMIN_REPORTS_KPIS: process.env.FEATURE_SF_ADMIN_REPORTS_KPIS === 'true',
  SERVER_FIRST_ADMIN_AUTH_DEBUG: process.env.FEATURE_SF_ADMIN_AUTH_DEBUG === 'true',

  // 🟡 Medium priority - Mixed patterns (already server-first but need updates)
  SERVER_FIRST_ADMIN_FINANCES: process.env.FEATURE_SF_ADMIN_FINANCES === 'true',
  SERVER_FIRST_ADMIN_WODS: process.env.FEATURE_SF_ADMIN_WODS === 'true',

  // 📊 Reports & Analytics
  SERVER_FIRST_ADMIN_REPORTS_CHECKINS: process.env.FEATURE_SF_ADMIN_REPORTS_CHECKINS === 'true',
  SERVER_FIRST_ADMIN_REPORTS_DASHBOARD: process.env.FEATURE_SF_ADMIN_REPORTS_DASHBOARD === 'true',
  SERVER_FIRST_ADMIN_REPORTS_NEW_MEMBERS: process.env.FEATURE_SF_ADMIN_REPORTS_NEW_MEMBERS === 'true',
  SERVER_FIRST_ADMIN_REPORTS: process.env.FEATURE_SF_ADMIN_REPORTS === 'true',

  // 👥 Member Management
  SERVER_FIRST_ADMIN_SUBSCRIPTIONS_ACTIVE: process.env.FEATURE_SF_ADMIN_SUBSCRIPTIONS_ACTIVE === 'true',
  SERVER_FIRST_ADMIN_SUBSCRIPTIONS_EXPIRING: process.env.FEATURE_SF_ADMIN_SUBSCRIPTIONS_EXPIRING === 'true',
  SERVER_FIRST_ADMIN_USERS_INDIVIDUAL: process.env.FEATURE_SF_ADMIN_USERS_INDIVIDUAL === 'true',
  SERVER_FIRST_ADMIN_CHECK_INS: process.env.FEATURE_SF_ADMIN_CHECK_INS === 'true',
  SERVER_FIRST_ADMIN_CHECK_ROLE: process.env.FEATURE_SF_ADMIN_CHECK_ROLE === 'true',

  // 💰 Financial Management
  SERVER_FIRST_ADMIN_FINANCES_ACCOUNTS: process.env.FEATURE_SF_ADMIN_FINANCES_ACCOUNTS === 'true',
  SERVER_FIRST_ADMIN_FINANCES_ANALYSIS: process.env.FEATURE_SF_ADMIN_FINANCES_ANALYSIS === 'true',
  SERVER_FIRST_ADMIN_FINANCES_CASHFLOW: process.env.FEATURE_SF_ADMIN_FINANCES_CASHFLOW === 'true',
  SERVER_FIRST_ADMIN_FINANCES_RECONCILIATION: process.env.FEATURE_SF_ADMIN_FINANCES_RECONCILIATION === 'true',
  SERVER_FIRST_ADMIN_FINANCES_TRANSACTIONS: process.env.FEATURE_SF_ADMIN_FINANCES_TRANSACTIONS === 'true',
  SERVER_FIRST_ADMIN_FINANCES_TRANSFERS: process.env.FEATURE_SF_ADMIN_FINANCES_TRANSFERS === 'true',
  SERVER_FIRST_ADMIN_FINANCES_WINBANK: process.env.FEATURE_SF_ADMIN_FINANCES_WINBANK === 'true',
  SERVER_FIRST_ADMIN_EXPENSES: process.env.FEATURE_SF_ADMIN_EXPENSES === 'true',
  SERVER_FIRST_ADMIN_PAYMENTS_ADD: process.env.FEATURE_SF_ADMIN_PAYMENTS_ADD === 'true',
  SERVER_FIRST_ADMIN_PAYMENTS_VIEW: process.env.FEATURE_SF_ADMIN_PAYMENTS_VIEW === 'true',
  SERVER_FIRST_ADMIN_INVOICES: process.env.FEATURE_SF_ADMIN_INVOICES === 'true',
  SERVER_FIRST_ADMIN_INVOICES_INDIVIDUAL: process.env.FEATURE_SF_ADMIN_INVOICES_INDIVIDUAL === 'true',
  SERVER_FIRST_ADMIN_INVOICES_VIEW: process.env.FEATURE_SF_ADMIN_INVOICES_VIEW === 'true',

  // 📅 Session Management
  SERVER_FIRST_ADMIN_SESSIONS_ADD: process.env.FEATURE_SF_ADMIN_SESSIONS_ADD === 'true',
  SERVER_FIRST_ADMIN_SESSIONS_CALENDAR: process.env.FEATURE_SF_ADMIN_SESSIONS_CALENDAR === 'true',
  SERVER_FIRST_ADMIN_SESSIONS_DAY: process.env.FEATURE_SF_ADMIN_SESSIONS_DAY === 'true',
  SERVER_FIRST_ADMIN_WEEKLY_BOOKINGS: process.env.FEATURE_SF_ADMIN_WEEKLY_BOOKINGS === 'true',

  // 💪 Content Management
  SERVER_FIRST_ADMIN_WODS_ADD: process.env.FEATURE_SF_ADMIN_WODS_ADD === 'true',
  SERVER_FIRST_ADMIN_WODS_BACK: process.env.FEATURE_SF_ADMIN_WODS_BACK === 'true',
  SERVER_FIRST_ADMIN_WODS_CONVERSIONTABLE: process.env.FEATURE_SF_ADMIN_WODS_CONVERSIONTABLE === 'true',
  SERVER_FIRST_ADMIN_WODS_DETAILS: process.env.FEATURE_SF_ADMIN_WODS_DETAILS === 'true',
  SERVER_FIRST_ADMIN_WODS_EDIT: process.env.FEATURE_SF_ADMIN_WODS_EDIT === 'true',
  SERVER_FIRST_ADMIN_WODS_EXERCISES: process.env.FEATURE_SF_ADMIN_WODS_EXERCISES === 'true',
  SERVER_FIRST_ADMIN_WODS_PRINT: process.env.FEATURE_SF_ADMIN_WODS_PRINT === 'true',
  SERVER_FIRST_ADMIN_EXERCISE_RECORDS: process.env.FEATURE_SF_ADMIN_EXERCISE_RECORDS === 'true',
  SERVER_FIRST_ADMIN_EXERCISE_ANALYTICS: process.env.FEATURE_SF_ADMIN_EXERCISE_ANALYTICS === 'true',
  SERVER_FIRST_ADMIN_EXERCISES_CONSOLIDATE: process.env.FEATURE_SF_ADMIN_EXERCISES_CONSOLIDATE === 'true',

  // 📞 Communication & Support
  SERVER_FIRST_ADMIN_SUPPORT: process.env.FEATURE_SF_ADMIN_SUPPORT === 'true',
  SERVER_FIRST_ADMIN_SUPPORT_THREAD: process.env.FEATURE_SF_ADMIN_SUPPORT_THREAD === 'true',
  SERVER_FIRST_ADMIN_NOTIFICATIONS: process.env.FEATURE_SF_ADMIN_NOTIFICATIONS === 'true',
  SERVER_FIRST_ADMIN_NOTIFICATIONS_DASHBOARD: process.env.FEATURE_SF_ADMIN_NOTIFICATIONS_DASHBOARD === 'true',
  SERVER_FIRST_ADMIN_NOTIFICATIONS_SUBSCRIPTIONS: process.env.FEATURE_SF_ADMIN_NOTIFICATIONS_SUBSCRIPTIONS === 'true',
  SERVER_FIRST_ADMIN_EMAIL_TEMPLATES: process.env.FEATURE_SF_ADMIN_EMAIL_TEMPLATES === 'true',

  // 🛠️ System & Tools
  SERVER_FIRST_ADMIN_DAILY: process.env.FEATURE_SF_ADMIN_DAILY === 'true',
  SERVER_FIRST_ADMIN_SETTINGS: process.env.FEATURE_SF_ADMIN_SETTINGS === 'true',
  SERVER_FIRST_ADMIN_CROSSFITIMER: process.env.FEATURE_SF_ADMIN_CROSSFITIMER === 'true',
  SERVER_FIRST_ADMIN_MERCHANDISE: process.env.FEATURE_SF_ADMIN_MERCHANDISE === 'true',
  SERVER_FIRST_ADMIN_MYDATA_LOGS: process.env.FEATURE_SF_ADMIN_MYDATA_LOGS === 'true',
  SERVER_FIRST_ADMIN_MYDATA_SETTINGS: process.env.FEATURE_SF_ADMIN_MYDATA_SETTINGS === 'true',
  SERVER_FIRST_ADMIN_MYDATA_TEST: process.env.FEATURE_SF_ADMIN_MYDATA_TEST === 'true',
  SERVER_FIRST_ADMIN_DIAGRAMS: process.env.FEATURE_SF_ADMIN_DIAGRAMS === 'true',

  // 🎯 Goals & Achievements
  SERVER_FIRST_ADMIN_GOALS: process.env.FEATURE_SF_ADMIN_GOALS === 'true',

  // 📈 Marketing
  SERVER_FIRST_ADMIN_MARKETING: process.env.FEATURE_SF_ADMIN_MARKETING === 'true',
}
```

## 🧩 **Component Feature Flags**

```typescript
const componentFlags = {
  // Core auth components
  SERVER_FIRST_AUTH_CONTEXT: process.env.FEATURE_SF_AUTH_CONTEXT === 'true',
  SERVER_FIRST_MAIN_HEADER: process.env.FEATURE_SF_MAIN_HEADER === 'true',
  SERVER_FIRST_MAIN_CONTENT: process.env.FEATURE_SF_MAIN_CONTENT === 'true',
  SERVER_FIRST_NOTIFICATIONS_POPOVER: process.env.FEATURE_SF_NOTIFICATIONS_POPOVER === 'true',

  // Navigation components
  SERVER_FIRST_ADMIN_SIDEBAR: process.env.FEATURE_SF_ADMIN_SIDEBAR === 'true',
  SERVER_FIRST_ADMIN_NAVIGATION_DRAWER: process.env.FEATURE_SF_ADMIN_NAVIGATION_DRAWER === 'true',
  SERVER_FIRST_SECONDARY_NAVIGATION: process.env.FEATURE_SF_SECONDARY_NAVIGATION === 'true',

  // Data components
  SERVER_FIRST_EXERCISES_CLIENT: process.env.FEATURE_SF_EXERCISES_CLIENT === 'true',
  SERVER_FIRST_WODS_CLIENT: process.env.FEATURE_SF_WODS_CLIENT === 'true',
  SERVER_FIRST_GOALS_MANAGER: process.env.FEATURE_SF_GOALS_MANAGER === 'true',
  SERVER_FIRST_KPI_DASHBOARD: process.env.FEATURE_SF_KPI_DASHBOARD === 'true',
  SERVER_FIRST_MYDATA_SETTINGS_CLIENT: process.env.FEATURE_SF_MYDATA_SETTINGS_CLIENT === 'true',

  // Financial components
  SERVER_FIRST_CASHFLOW_SUMMARY_CARDS: process.env.FEATURE_SF_CASHFLOW_SUMMARY_CARDS === 'true',
  SERVER_FIRST_INVOICE_MANAGER: process.env.FEATURE_SF_INVOICE_MANAGER === 'true',

  // User management components
  SERVER_FIRST_PELATES_EDIT: process.env.FEATURE_SF_PELATES_EDIT === 'true',
  SERVER_FIRST_TAG_MANAGER: process.env.FEATURE_SF_TAG_MANAGER === 'true',
  SERVER_FIRST_EMAIL_COMPOSER: process.env.FEATURE_SF_EMAIL_COMPOSER === 'true',
}
```

## 📝 **Environment Variables Setup**

### **Initial Setup (.env.local) - All Disabled**
```bash
# Master switches
FEATURE_SERVER_FIRST_AUTH=false
FEATURE_SF_USER_SECTION=false
FEATURE_SF_ADMIN_SECTION=false

# User pages - High Priority
FEATURE_SF_USER_BOOKINGS=false
FEATURE_SF_USER_STRENGTH_PROGRAM=false
FEATURE_SF_USER_WODS=false
FEATURE_SF_USER_WODS_TODAY=false

# User pages - Medium Priority
FEATURE_SF_USER_GOALS=false
FEATURE_SF_USER_EXERCISE_RECORDS=false
FEATURE_SF_USER_ACHIEVEMENTS=false
FEATURE_SF_USER_PROFILE=false
FEATURE_SF_USER_PROFILE_EDIT=false
FEATURE_SF_USER_PROFILE_ONBOARDING=false

# User pages - Low Priority
FEATURE_SF_USER_FRONT_PAGE=false
FEATURE_SF_USER_FITNESS_ASSESSMENT=false
FEATURE_SF_USER_INJURY_REPORT=false
FEATURE_SF_USER_LOWBACKPAIN_ASSESSMENT=false
FEATURE_SF_USER_NOTIFICATIONS=false
FEATURE_SF_USER_OBESE_EXERCISE_PROGRAM=false
FEATURE_SF_USER_PRICING=false
FEATURE_SF_USER_SESSION_BOOK=false
FEATURE_SF_USER_SESSIONS_BOOKINGS=false
FEATURE_SF_USER_SUPPORT=false
FEATURE_SF_USER_SUPPORT_THREAD=false
FEATURE_SF_USER_WEIGHT_TRACKER=false

# User strength program sub-pages
FEATURE_SF_USER_STRENGTH_ADMIN=false
FEATURE_SF_USER_STRENGTH_CUSTOM_WORKOUT=false
FEATURE_SF_USER_STRENGTH_LOG=false
FEATURE_SF_USER_STRENGTH_NEXT_WORKOUT=false
FEATURE_SF_USER_STRENGTH_SCHEDULE=false
FEATURE_SF_USER_STRENGTH_SETUP=false

# Admin pages - Critical
FEATURE_SF_ADMIN_LAYOUT=false
FEATURE_SF_ADMIN_USERS_VIEW=false
FEATURE_SF_ADMIN_REPORTS_KPIS=false
FEATURE_SF_ADMIN_AUTH_DEBUG=false

# Admin pages - Medium Priority
FEATURE_SF_ADMIN_FINANCES=false
FEATURE_SF_ADMIN_WODS=false

# Admin reports
FEATURE_SF_ADMIN_REPORTS_CHECKINS=false
FEATURE_SF_ADMIN_REPORTS_DASHBOARD=false
FEATURE_SF_ADMIN_REPORTS_NEW_MEMBERS=false
FEATURE_SF_ADMIN_REPORTS=false

# Admin member management
FEATURE_SF_ADMIN_SUBSCRIPTIONS_ACTIVE=false
FEATURE_SF_ADMIN_SUBSCRIPTIONS_EXPIRING=false
FEATURE_SF_ADMIN_USERS_INDIVIDUAL=false
FEATURE_SF_ADMIN_CHECK_INS=false
FEATURE_SF_ADMIN_CHECK_ROLE=false

# Admin financial management
FEATURE_SF_ADMIN_FINANCES_ACCOUNTS=false
FEATURE_SF_ADMIN_FINANCES_ANALYSIS=false
FEATURE_SF_ADMIN_FINANCES_CASHFLOW=false
FEATURE_SF_ADMIN_FINANCES_RECONCILIATION=false
FEATURE_SF_ADMIN_FINANCES_TRANSACTIONS=false
FEATURE_SF_ADMIN_FINANCES_TRANSFERS=false
FEATURE_SF_ADMIN_FINANCES_WINBANK=false
FEATURE_SF_ADMIN_EXPENSES=false
FEATURE_SF_ADMIN_PAYMENTS_ADD=false
FEATURE_SF_ADMIN_PAYMENTS_VIEW=false
FEATURE_SF_ADMIN_INVOICES=false
FEATURE_SF_ADMIN_INVOICES_INDIVIDUAL=false
FEATURE_SF_ADMIN_INVOICES_VIEW=false

# Admin session management
FEATURE_SF_ADMIN_SESSIONS_ADD=false
FEATURE_SF_ADMIN_SESSIONS_CALENDAR=false
FEATURE_SF_ADMIN_SESSIONS_DAY=false
FEATURE_SF_ADMIN_WEEKLY_BOOKINGS=false

# Admin content management
FEATURE_SF_ADMIN_WODS_ADD=false
FEATURE_SF_ADMIN_WODS_BACK=false
FEATURE_SF_ADMIN_WODS_CONVERSIONTABLE=false
FEATURE_SF_ADMIN_WODS_DETAILS=false
FEATURE_SF_ADMIN_WODS_EDIT=false
FEATURE_SF_ADMIN_WODS_EXERCISES=false
FEATURE_SF_ADMIN_WODS_PRINT=false
FEATURE_SF_ADMIN_EXERCISE_RECORDS=false
FEATURE_SF_ADMIN_EXERCISE_ANALYTICS=false
FEATURE_SF_ADMIN_EXERCISES_CONSOLIDATE=false

# Admin communication
FEATURE_SF_ADMIN_SUPPORT=false
FEATURE_SF_ADMIN_SUPPORT_THREAD=false
FEATURE_SF_ADMIN_NOTIFICATIONS=false
FEATURE_SF_ADMIN_NOTIFICATIONS_DASHBOARD=false
FEATURE_SF_ADMIN_NOTIFICATIONS_SUBSCRIPTIONS=false
FEATURE_SF_ADMIN_EMAIL_TEMPLATES=false

# Admin system tools
FEATURE_SF_ADMIN_DAILY=false
FEATURE_SF_ADMIN_SETTINGS=false
FEATURE_SF_ADMIN_CROSSFITIMER=false
FEATURE_SF_ADMIN_MERCHANDISE=false
FEATURE_SF_ADMIN_MYDATA_LOGS=false
FEATURE_SF_ADMIN_MYDATA_SETTINGS=false
FEATURE_SF_ADMIN_MYDATA_TEST=false
FEATURE_SF_ADMIN_DIAGRAMS=false
FEATURE_SF_ADMIN_GOALS=false
FEATURE_SF_ADMIN_MARKETING=false

# Component flags
FEATURE_SF_AUTH_CONTEXT=false
FEATURE_SF_MAIN_HEADER=false
FEATURE_SF_MAIN_CONTENT=false
FEATURE_SF_NOTIFICATIONS_POPOVER=false
FEATURE_SF_ADMIN_SIDEBAR=false
FEATURE_SF_ADMIN_NAVIGATION_DRAWER=false
FEATURE_SF_SECONDARY_NAVIGATION=false
FEATURE_SF_EXERCISES_CLIENT=false
FEATURE_SF_WODS_CLIENT=false
FEATURE_SF_GOALS_MANAGER=false
FEATURE_SF_KPI_DASHBOARD=false
FEATURE_SF_MYDATA_SETTINGS_CLIENT=false
FEATURE_SF_CASHFLOW_SUMMARY_CARDS=false
FEATURE_SF_INVOICE_MANAGER=false
FEATURE_SF_PELATES_EDIT=false
FEATURE_SF_TAG_MANAGER=false
FEATURE_SF_EMAIL_COMPOSER=false

# Gradual rollout percentage (0-100)
ROLLOUT_PERCENTAGE=0
```

## 🚀 **Migration Scripts**

### **Enable Feature Scripts:**
```bash
# scripts/enable-features.sh

# Enable first user page
enable_user_bookings() {
  export FEATURE_SERVER_FIRST_AUTH=true
  export FEATURE_SF_USER_SECTION=true
  export FEATURE_SF_USER_BOOKINGS=true
  echo "✅ User bookings server-first enabled"
}

# Enable first admin page
enable_admin_finances() {
  export FEATURE_SERVER_FIRST_AUTH=true
  export FEATURE_SF_ADMIN_SECTION=true
  export FEATURE_SF_ADMIN_FINANCES=true
  echo "✅ Admin finances server-first enabled"
}

# Enable all user pages
enable_all_user_pages() {
  export FEATURE_SF_USER_SECTION=true
  # Set all FEATURE_SF_USER_* to true
  echo "✅ All user pages server-first enabled"
}

# Enable all admin pages
enable_all_admin_pages() {
  export FEATURE_SF_ADMIN_SECTION=true
  # Set all FEATURE_SF_ADMIN_* to true
  echo "✅ All admin pages server-first enabled"
}
```

## 📊 **Usage Examples**

### **Page Implementation Example:**
```typescript
// app/user/bookings/page.tsx
import { FeatureFlags } from '@/utils/feature-flags'

export default async function BookingsPage() {
  if (FeatureFlags.SERVER_FIRST_USER_BOOKINGS) {
    // Server-first implementation
    const auth = await getServerAuth()
    if (!auth.user) redirect('/auth')

    const bookings = await fetchUserBookings(auth.pelatiId)
    return <ServerFirstBookings initialData={bookings} userAuth={auth} />
  } else {
    // Legacy implementation
    return <LegacyBookings />
  }
}
```

### **Component Implementation Example:**
```typescript
// components/goals_badges/GoalsManager.tsx
import { FeatureFlags } from '@/utils/feature-flags'

export function GoalsManager(props) {
  if (FeatureFlags.SERVER_FIRST_GOALS_MANAGER && props.initialGoals) {
    // Server-first implementation
    return <ServerFirstGoalsManager {...props} />
  } else {
    // Legacy implementation
    return <LegacyGoalsManager {...props} />
  }
}
```

## 🎯 **Recommended Migration Order**

### **Phase 1: Foundation (Week 1)**
```bash
FEATURE_SERVER_FIRST_AUTH=true
FEATURE_SF_AUTH_CONTEXT=true
```

### **Phase 2: User Pages (Weeks 2-7)**
```bash
# Week 2
FEATURE_SF_USER_SECTION=true
FEATURE_SF_USER_BOOKINGS=true

# Week 3
FEATURE_SF_USER_WODS=true

# Week 4
FEATURE_SF_USER_ACHIEVEMENTS=true

# Week 5
FEATURE_SF_USER_EXERCISE_RECORDS=true

# Week 6
FEATURE_SF_USER_GOALS=true
FEATURE_SF_GOALS_MANAGER=true

# Week 7
FEATURE_SF_USER_STRENGTH_PROGRAM=true
```

### **Phase 3: Admin Pages (Weeks 8-12)**
```bash
# Week 8
FEATURE_SF_ADMIN_SECTION=true
FEATURE_SF_ADMIN_FINANCES=true

# Week 9
FEATURE_SF_ADMIN_WODS=true

# Week 10
FEATURE_SF_ADMIN_REPORTS_KPIS=true
FEATURE_SF_KPI_DASHBOARD=true

# Week 11
FEATURE_SF_ADMIN_USERS_VIEW=true

# Week 12
FEATURE_SF_ADMIN_LAYOUT=true
```

This comprehensive feature flag system allows you to migrate each page individually while keeping your app fully functional!
```
