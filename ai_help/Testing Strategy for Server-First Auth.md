# 🧪 Testing Strategy for Server-First Auth Conversion

## 🎯 **Testing Objectives**

1. **Verify auth works correctly** - Users can access appropriate pages
2. **Ensure no regressions** - Existing functionality still works
3. **Validate performance improvements** - Faster page loads, no auth waterfalls
4. **Check security** - Unauthorized access is properly blocked
5. **Test edge cases** - Session expiry, role changes, etc.

## 📋 **Test Categories**

### **1. Unit Tests** - Component Level
### **2. Integration Tests** - Page Level
### **3. E2E Tests** - User Journey Level
### **4. Performance Tests** - Load Time & Auth Speed
### **5. Security Tests** - Access Control & Role Verification

## 🔧 **Test Setup & Tools**

### **Testing Stack:**
```json
{
  "unit": "Jest + React Testing Library",
  "integration": "Jest + MSW (Mock Service Worker)",
  "e2e": "Playwright or Cypress",
  "performance": "Lighthouse + Custom metrics",
  "security": "Custom auth flow tests"
}
```

### **Test Database Setup:**
```typescript
// tests/setup/test-db.ts
export const setupTestDatabase = async () => {
  // Create test users with different roles
  const testUsers = [
    { email: '<EMAIL>', role: 'admin' },
    { email: '<EMAIL>', role: 'user' },
    { email: '<EMAIL>', role: null }
  ]

  // Setup test data for each page type
  await createTestData()
}
```

## 🧪 **1. Unit Tests - Component Level**

### **Test Template for Converted Components:**
```typescript
// tests/components/UserPageClient.test.tsx
import { render, screen } from '@testing-library/react'
import { UserPageClient } from '@/app/user/some-page/client'

describe('UserPageClient (Server-First)', () => {
  const mockAuth = {
    user: { id: 'user-123', email: '<EMAIL>' },
    isAdmin: false,
    pelatiId: 'pelati-123'
  }

  const mockInitialData = [
    { id: 1, name: 'Test Data' }
  ]

  it('renders with server-provided auth and data', () => {
    render(
      <UserPageClient
        userAuth={mockAuth}
        initialData={mockInitialData}
      />
    )

    expect(screen.getByText('Test Data')).toBeInTheDocument()
    // Should not show loading states for initial data
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument()
  })

  it('handles auth state correctly', () => {
    render(<UserPageClient userAuth={mockAuth} initialData={[]} />)

    // Should show user-specific content
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
    // Should not show admin content
    expect(screen.queryByText('Admin Panel')).not.toBeInTheDocument()
  })

  it('handles missing auth gracefully', () => {
    const noAuth = { user: null, isAdmin: false, pelatiId: null }
    render(<UserPageClient userAuth={noAuth} initialData={[]} />)

    // Should handle gracefully (component should not crash)
    expect(screen.getByText('Please sign in')).toBeInTheDocument()
  })
})
```

### **Component Test Checklist:**
```typescript
// tests/utils/component-test-checklist.ts
export const testServerFirstComponent = (Component: React.FC, props: any) => {
  describe(`${Component.name} Server-First Tests`, () => {
    // ✅ Renders with server props
    it('renders with server-provided data')

    // ✅ No loading states for initial data
    it('does not show loading for initial data')

    // ✅ Handles auth state correctly
    it('respects user permissions')

    // ✅ Handles missing/invalid auth
    it('handles auth edge cases')

    // ✅ Client-side interactions work
    it('handles user interactions correctly')

    // ✅ Form submissions work
    it('submits forms with proper auth')
  })
}
```

## 🔗 **2. Integration Tests - Page Level**

### **Server Component Integration Tests:**
```typescript
// tests/pages/user/bookings.test.tsx
import { GET } from '@/app/user/bookings/page'
import { createMockRequest } from '@/tests/utils/mock-request'

describe('/user/bookings - Server Component', () => {
  it('redirects unauthenticated users', async () => {
    const request = createMockRequest({ authenticated: false })

    // Should redirect to /auth
    expect(await GET(request)).toMatchObject({
      status: 302,
      headers: { location: '/auth' }
    })
  })

  it('fetches user bookings for authenticated users', async () => {
    const request = createMockRequest({
      authenticated: true,
      userId: 'user-123'
    })

    const response = await GET(request)
    const html = await response.text()

    // Should contain user's bookings
    expect(html).toContain('My Bookings')
    expect(html).toContain('user-123-booking-data')
  })

  it('handles users without pelati profile', async () => {
    const request = createMockRequest({
      authenticated: true,
      userId: 'user-no-profile'
    })

    const response = await GET(request)

    // Should redirect to profile setup
    expect(response.status).toBe(302)
    expect(response.headers.get('location')).toBe('/user/profile/onboarding')
  })
})
```

### **Page Conversion Test Template:**
```typescript
// tests/utils/page-conversion-test.ts
export const testPageConversion = (pagePath: string) => {
  describe(`${pagePath} - Server-First Conversion`, () => {
    // ✅ Server auth works
    it('handles server-side authentication')

    // ✅ Data fetching works
    it('fetches initial data on server')

    // ✅ Client hydration works
    it('hydrates client components correctly')

    // ✅ No auth waterfalls
    it('does not create auth waterfalls')

    // ✅ Role-based access
    it('enforces role-based access control')

    // ✅ Error handling
    it('handles auth errors gracefully')
  })
}
```

## 🎭 **3. E2E Tests - User Journey Level**

### **Auth Flow E2E Tests:**
```typescript
// tests/e2e/auth-flows.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Server-First Auth Flows', () => {
  test('user login and page access', async ({ page }) => {
    // 1. Visit protected page while logged out
    await page.goto('/user/bookings')
    await expect(page).toHaveURL('/auth')

    // 2. Login via magic link
    await page.fill('[data-testid=email-input]', '<EMAIL>')
    await page.click('[data-testid=signin-button]')

    // 3. Simulate magic link click (in test environment)
    await page.goto('/auth/callback?code=test-auth-code')

    // 4. Should redirect to originally requested page
    await expect(page).toHaveURL('/user/bookings')

    // 5. Page should load without loading states
    await expect(page.locator('[data-testid=loading]')).not.toBeVisible()
    await expect(page.locator('[data-testid=bookings-content]')).toBeVisible()
  })

  test('admin access control', async ({ page }) => {
    // Login as regular user
    await loginAsUser(page, '<EMAIL>')

    // Try to access admin page
    await page.goto('/admin/users/view')

    // Should redirect to user area
    await expect(page).toHaveURL('/user/front-page')

    // Login as admin
    await loginAsUser(page, '<EMAIL>')
    await page.goto('/admin/users/view')

    // Should access admin page
    await expect(page).toHaveURL('/admin/users/view')
    await expect(page.locator('[data-testid=admin-content]')).toBeVisible()
  })
})
```

### **Page-Specific E2E Tests:**
```typescript
// tests/e2e/user-pages.spec.ts
test.describe('User Pages - Server-First', () => {
  test.beforeEach(async ({ page }) => {
    await loginAsUser(page, '<EMAIL>')
  })

  test('bookings page works correctly', async ({ page }) => {
    await page.goto('/user/bookings')

    // Should load immediately (no loading states)
    await expect(page.locator('[data-testid=bookings-list]')).toBeVisible()

    // Should show user's bookings
    await expect(page.locator('[data-testid=booking-item]')).toHaveCount(3)

    // Cancel booking should work
    await page.click('[data-testid=cancel-booking-1]')
    await expect(page.locator('[data-testid=booking-item]')).toHaveCount(2)
  })

  test('strength program page works correctly', async ({ page }) => {
    await page.goto('/user/strength-program')

    // Should show today's workout immediately
    await expect(page.locator('[data-testid=todays-workout]')).toBeVisible()

    // Should not show loading spinners for initial data
    await expect(page.locator('[data-testid=loading-workout]')).not.toBeVisible()

    // Workout completion should work
    await page.click('[data-testid=complete-workout]')
    await expect(page.locator('[data-testid=workout-completed]')).toBeVisible()
  })
})
```

## ⚡ **4. Performance Tests**

### **Page Load Performance:**
```typescript
// tests/performance/page-load.test.ts
import { chromium } from 'playwright'

describe('Page Load Performance - Server-First', () => {
  it('user pages load faster than 2 seconds', async () => {
    const browser = await chromium.launch()
    const page = await browser.newPage()

    // Setup authenticated session
    await setupAuthenticatedSession(page)

    const userPages = [
      '/user/bookings',
      '/user/strength-program',
      '/user/wods',
      '/user/goals'
    ]

    for (const pagePath of userPages) {
      const startTime = Date.now()

      await page.goto(pagePath)
      await page.waitForSelector('[data-testid=page-content]')

      const loadTime = Date.now() - startTime

      expect(loadTime).toBeLessThan(2000) // 2 seconds
      console.log(`${pagePath}: ${loadTime}ms`)
    }
  })

  it('no auth waterfalls detected', async () => {
    const page = await browser.newPage()

    // Monitor network requests
    const authRequests: string[] = []
    page.on('request', (request) => {
      if (request.url().includes('/auth/') ||
          request.url().includes('supabase.co/auth/')) {
        authRequests.push(request.url())
      }
    })

    await setupAuthenticatedSession(page)
    await page.goto('/user/bookings')
    await page.waitForSelector('[data-testid=page-content]')

    // Should have minimal auth requests (ideally 0 for cached sessions)
    expect(authRequests.length).toBeLessThan(2)
  })
})
```

### **Lighthouse Performance Tests:**
```typescript
// tests/performance/lighthouse.test.ts
import lighthouse from 'lighthouse'
import { chromium } from 'playwright'

describe('Lighthouse Performance Tests', () => {
  it('user pages meet performance benchmarks', async () => {
    const browser = await chromium.launch()
    const page = await browser.newPage()

    await setupAuthenticatedSession(page)

    const results = await lighthouse('/user/bookings', {
      port: 9222,
      onlyCategories: ['performance'],
    })

    expect(results.lhr.categories.performance.score).toBeGreaterThan(0.9)
    expect(results.lhr.audits['first-contentful-paint'].numericValue).toBeLessThan(2000)
    expect(results.lhr.audits['largest-contentful-paint'].numericValue).toBeLessThan(3000)
  })
})
```

## 🔒 **5. Security Tests**

### **Access Control Tests:**
```typescript
// tests/security/access-control.test.ts
describe('Access Control - Server-First', () => {
  it('blocks unauthorized access to user pages', async () => {
    const response = await fetch('/user/bookings', {
      headers: { 'Cookie': '' } // No auth cookie
    })

    expect(response.status).toBe(302)
    expect(response.headers.get('location')).toBe('/auth')
  })

  it('blocks non-admin access to admin pages', async () => {
    const userSession = await createUserSession('<EMAIL>')

    const response = await fetch('/admin/users/view', {
      headers: { 'Cookie': userSession.cookie }
    })

    expect(response.status).toBe(302)
    expect(response.headers.get('location')).toBe('/user/front-page')
  })

  it('allows admin access to admin pages', async () => {
    const adminSession = await createAdminSession('<EMAIL>')

    const response = await fetch('/admin/users/view', {
      headers: { 'Cookie': adminSession.cookie }
    })

    expect(response.status).toBe(200)
    expect(await response.text()).toContain('Admin Users View')
  })

  it('handles session expiry correctly', async () => {
    const expiredSession = await createExpiredSession()

    const response = await fetch('/user/bookings', {
      headers: { 'Cookie': expiredSession.cookie }
    })

    expect(response.status).toBe(302)
    expect(response.headers.get('location')).toContain('/auth')
  })
})
```

### **Role-Based Access Tests:**
```typescript
// tests/security/role-based-access.test.ts
describe('Role-Based Access Control', () => {
  const testCases = [
    { role: null, allowedPages: ['/auth', '/guest/calendar'], blockedPages: ['/user/bookings', '/admin/users'] },
    { role: 'user', allowedPages: ['/user/bookings', '/user/profile'], blockedPages: ['/admin/users', '/admin/finances'] },
    { role: 'admin', allowedPages: ['/user/bookings', '/admin/users', '/admin/finances'], blockedPages: [] }
  ]

  testCases.forEach(({ role, allowedPages, blockedPages }) => {
    describe(`${role || 'unauthenticated'} user`, () => {
      let session: any

      beforeEach(async () => {
        session = role ? await createSessionWithRole(role) : null
      })

      allowedPages.forEach(page => {
        it(`can access ${page}`, async () => {
          const response = await fetch(page, {
            headers: session ? { 'Cookie': session.cookie } : {}
          })
          expect(response.status).not.toBe(302) // Not redirected
        })
      })

      blockedPages.forEach(page => {
        it(`cannot access ${page}`, async () => {
          const response = await fetch(page, {
            headers: session ? { 'Cookie': session.cookie } : {}
          })
          expect(response.status).toBe(302) // Redirected
        })
      })
    })
  })
})
```

## 🚀 **6. Test Automation & CI/CD**

### **Test Scripts:**
```json
// package.json
{
  "scripts": {
    "test:unit": "jest --testPathPattern=tests/unit",
    "test:integration": "jest --testPathPattern=tests/integration",
    "test:e2e": "playwright test",
    "test:performance": "jest --testPathPattern=tests/performance",
    "test:security": "jest --testPathPattern=tests/security",
    "test:auth-conversion": "npm run test:unit && npm run test:integration && npm run test:security",
    "test:full": "npm run test:auth-conversion && npm run test:e2e && npm run test:performance"
  }
}
```

### **GitHub Actions Workflow:**
```yaml
# .github/workflows/auth-conversion-tests.yml
name: Server-First Auth Tests

on:
  push:
    paths:
      - 'app/user/**'
      - 'app/admin/**'
      - 'components/**'
      - 'utils/auth-server.ts'
      - 'contexts/AuthContext.tsx'

jobs:
  auth-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Setup Test Database
        run: |
          docker run -d --name test-supabase \
            -p 54322:5432 \
            -e POSTGRES_PASSWORD=test \
            supabase/postgres:**********

      - name: Run Unit Tests
        run: npm run test:unit

      - name: Run Integration Tests
        run: npm run test:integration

      - name: Run Security Tests
        run: npm run test:security

      - name: Run E2E Tests
        run: npm run test:e2e

      - name: Performance Tests
        run: npm run test:performance
```

## 📊 **7. Test Coverage & Metrics**

### **Coverage Requirements:**
```typescript
// jest.config.js
module.exports = {
  collectCoverageFrom: [
    'app/user/**/*.{ts,tsx}',
    'app/admin/**/*.{ts,tsx}',
    'components/**/*.{ts,tsx}',
    'utils/auth-server.ts',
    'contexts/AuthContext.tsx'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './utils/auth-server.ts': {
      branches: 95,
      functions: 95,
      lines: 95,
      statements: 95
    }
  }
}
```

### **Test Metrics Dashboard:**
```typescript
// tests/utils/metrics.ts
export const trackConversionMetrics = () => {
  return {
    pagesConverted: countConvertedPages(),
    componentsUpdated: countUpdatedComponents(),
    testCoverage: getCoveragePercentage(),
    performanceGains: measurePerformanceGains(),
    securityIssues: countSecurityIssues()
  }
}
```

## 🎯 **8. Page-Specific Test Checklist**

### **For Each Converted Page:**
```typescript
// tests/utils/page-test-checklist.ts
export const pageTestChecklist = (pagePath: string) => {
  return {
    // ✅ Authentication Tests
    'redirects unauthenticated users': () => testUnauthenticatedRedirect(pagePath),
    'allows authenticated users': () => testAuthenticatedAccess(pagePath),
    'enforces role requirements': () => testRoleRequirements(pagePath),

    // ✅ Performance Tests
    'loads in under 2 seconds': () => testPageLoadTime(pagePath),
    'has no auth waterfalls': () => testNoAuthWaterfalls(pagePath),
    'meets Lighthouse scores': () => testLighthouseScores(pagePath),

    // ✅ Functionality Tests
    'displays initial data correctly': () => testInitialDataDisplay(pagePath),
    'handles user interactions': () => testUserInteractions(pagePath),
    'submits forms correctly': () => testFormSubmissions(pagePath),

    // ✅ Error Handling Tests
    'handles auth errors gracefully': () => testAuthErrorHandling(pagePath),
    'handles network errors': () => testNetworkErrorHandling(pagePath),
    'handles missing data': () => testMissingDataHandling(pagePath)
  }
}
```

## 🔧 **9. Test Utilities & Helpers**

### **Auth Test Helpers:**
```typescript
// tests/utils/auth-helpers.ts
export const createTestSession = async (userEmail: string, role: string = 'user') => {
  const user = await createTestUser(userEmail, role)
  const session = await supabase.auth.signInWithPassword({
    email: userEmail,
    password: 'test-password'
  })
  return session
}

export const mockServerAuth = (overrides = {}) => ({
  user: { id: 'test-user', email: '<EMAIL>' },
  isAdmin: false,
  pelatiId: 'test-pelati',
  roles: ['user'],
  ...overrides
})

export const setupAuthenticatedRequest = (role: string = 'user') => {
  // Setup request with proper auth headers/cookies
}
```

## 📝 **10. Testing Workflow**

### **Before Converting a Page:**
1. **Write failing tests** for the new server-first behavior
2. **Document current behavior** with existing tests
3. **Set performance benchmarks** for comparison

### **During Conversion:**
1. **Run tests continuously** to catch regressions
2. **Update tests** as you change the implementation
3. **Add new tests** for server-first specific features

### **After Conversion:**
1. **Verify all tests pass**
2. **Check performance improvements**
3. **Run security tests**
4. **Update documentation**

This comprehensive testing strategy ensures that your server-first auth conversion is reliable, secure, and performant!
```
