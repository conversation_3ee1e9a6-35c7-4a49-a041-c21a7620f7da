// components/invoices/InvoiceManagementDashboard.tsx
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Plus,
  Filter,
  RefreshCw,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';
import Link from 'next/link';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { toast } from 'sonner';
import InvoiceTable from './InvoiceTable';
import BulkActions from './BulkActions';
import InvoiceCheckerModal from './InvoiceCheckerModal';
import DebugActions from './DebugActions';
import type { Database } from '@/types/supabase';

type Invoice = Database['public']['Tables']['invoices']['Row'];

interface InvoiceManagementDashboardProps {
  initialInvoices: Invoice[];
}

export default function InvoiceManagementDashboard({
  initialInvoices
}: InvoiceManagementDashboardProps) {
  const [invoices, setInvoices] = useState<Invoice[]>(initialInvoices);
  const [filteredInvoices, setFilteredInvoices] = useState<Invoice[]>(initialInvoices);
  const [selectedInvoices, setSelectedInvoices] = useState<Invoice[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');

  // Modal states
  const [checkerModalOpen, setCheckerModalOpen] = useState(false);
  const [selectedInvoiceForChecker, setSelectedInvoiceForChecker] = useState<Invoice | null>(null);

  const supabase = createClientComponentClient<Database>();

  // Calculate statistics
  const stats = {
    total: invoices.length,
    draft: invoices.filter(inv => inv.status === 'draft').length,
    submitted: invoices.filter(inv => inv.status === 'submitted').length,
    error: invoices.filter(inv => inv.status === 'error').length,
    totalValue: invoices.reduce((sum, inv) => sum + inv.total_gross, 0)
  };

  // Refresh invoices data
  const refreshInvoices = async () => {
    setIsRefreshing(true);
    try {
      const { data, error } = await supabase
        .from('invoices')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      setInvoices(data || []);
      toast.success('Invoices refreshed');
    } catch (error) {
      console.error('Error refreshing invoices:', error);
      toast.error('Failed to refresh invoices');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Filter invoices based on search and filters
  useEffect(() => {
    let filtered = [...invoices];

    // Search filter
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(invoice =>
        `${invoice.invoice_series}-${invoice.invoice_number}`.toLowerCase().includes(searchLower) ||
        invoice.client_name?.toLowerCase().includes(searchLower) ||
        invoice.client_vat?.toLowerCase().includes(searchLower) ||
        invoice.mark?.toLowerCase().includes(searchLower)
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(invoice => invoice.status === statusFilter);
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      const filterDate = new Date();

      switch (dateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          filtered = filtered.filter(invoice =>
            invoice.created_at && new Date(invoice.created_at).getTime() >= filterDate.getTime()
          );
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          filtered = filtered.filter(invoice =>
            invoice.created_at && new Date(invoice.created_at).getTime() >= filterDate.getTime()
          );
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          filtered = filtered.filter(invoice =>
            invoice.created_at && new Date(invoice.created_at).getTime() >= filterDate.getTime()
          );
          break;
      }
    }

    setFilteredInvoices(filtered);

    // Clear selection if selected invoices are no longer visible
    const visibleIds = filtered.map(inv => inv.id);
    setSelectedInvoices(prev => prev.filter(inv => visibleIds.includes(inv.id)));
  }, [invoices, searchTerm, statusFilter, dateFilter]);

  // Handle showing checker modal
  const handleShowChecker = (invoice: Invoice) => {
    setSelectedInvoiceForChecker(invoice);
    setCheckerModalOpen(true);
  };

  // Handle showing add line modal (placeholder - would need implementation)
  const handleShowAddLine = (invoice: Invoice) => {
    toast.info(`Add line functionality for invoice ${invoice.invoice_number} - would show modal for adding lines to invoice`);
    // This would open a modal with AddInvoiceLineForm
  };

  // Handle showing add payment modal (placeholder - would need implementation)
  const handleShowAddPayment = (invoice: Invoice) => {
    toast.info(`Add payment functionality for invoice ${invoice.invoice_number} - would show modal for adding payment methods`);
    // This would open a modal with AddPaymentMethodForm
  };

  // Handle action completion (refresh data)
  const handleActionComplete = () => {
    refreshInvoices();
  };

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invoices</CardTitle>
            <BarChart3 className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Draft</CardTitle>
            <Clock className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.draft}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submitted</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.submitted}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Errors</CardTitle>
            <AlertCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.error}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <BarChart3 className="h-4 w-4 text-gray-500" />
          </CardHeader>
          <CardContent>
            <div className="text-lg font-bold">
              €{stats.totalValue.toLocaleString('el-GR', { minimumFractionDigits: 2 })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Actions */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="flex flex-1 gap-4 items-center">
          {/* Search */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Search invoices..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Status Filter */}
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="draft">Draft</SelectItem>
              <SelectItem value="submitted">Submitted</SelectItem>
              <SelectItem value="error">Error</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="canceled">Canceled</SelectItem>
            </SelectContent>
          </Select>

          {/* Date Filter */}
          <Select value={dateFilter} onValueChange={setDateFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Date" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="week">Last Week</SelectItem>
              <SelectItem value="month">Last Month</SelectItem>
            </SelectContent>
          </Select>

          {/* Active filters indicator */}
          {(searchTerm || statusFilter !== 'all' || dateFilter !== 'all') && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Filter className="h-3 w-3" />
              {[
                searchTerm && 'search',
                statusFilter !== 'all' && statusFilter,
                dateFilter !== 'all' && dateFilter
              ].filter(Boolean).length} filter(s)
            </Badge>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <DebugActions />

          <Button
            variant="outline"
            onClick={refreshInvoices}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Link href="/admin/mydata/test">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Test Invoice
            </Button>
          </Link>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedInvoices.length > 0 && (
        <BulkActions
          selectedInvoices={selectedInvoices}
          onClearSelection={() => setSelectedInvoices([])}
          onActionComplete={handleActionComplete}
        />
      )}

      {/* Results Summary */}
      <div className="flex items-center justify-between text-sm text-gray-500">
        <span>
          Showing {filteredInvoices.length} of {invoices.length} invoices
        </span>
        {selectedInvoices.length > 0 && (
          <span>
            {selectedInvoices.length} selected
          </span>
        )}
      </div>

      {/* Invoice Table */}
      <InvoiceTable
        invoices={filteredInvoices}
        selectedInvoices={selectedInvoices}
        onSelectionChange={setSelectedInvoices}
        onActionComplete={handleActionComplete}
        onShowChecker={handleShowChecker}
        onShowAddLine={handleShowAddLine}
        onShowAddPayment={handleShowAddPayment}
      />

      {/* Modals */}
      <InvoiceCheckerModal
        invoice={selectedInvoiceForChecker}
        open={checkerModalOpen}
        onOpenChange={setCheckerModalOpen}
        onActionComplete={handleActionComplete}
      />
    </div>
  );
}